#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""串口测试工具 - 全面测试串口通信功能"""

import serial
import struct
import time
import os
import sys


class SerialTester:
    def __init__(self, port='/dev/ttyTHS0', baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.connection = None
        
    def check_port_exists(self):
        """检查串口设备是否存在"""
        print(f"1. 检查串口设备 {self.port} 是否存在...")
        if os.path.exists(self.port):
            print("   ✓ 串口设备存在")
            return True
        else:
            print("   ✗ 串口设备不存在")
            return False
    
    def check_permissions(self):
        """检查串口权限"""
        print("2. 检查串口权限...")
        try:
            stat_info = os.stat(self.port)
            print(f"   串口权限: {oct(stat_info.st_mode)}")
            
            # 检查当前用户是否在dialout组
            import grp
            try:
                dialout_group = grp.getgrnam('dialout')
                current_user = os.getlogin()
                if current_user in dialout_group.gr_mem:
                    print("   ✓ 用户在dialout组中")
                else:
                    print("   ⚠ 用户不在dialout组中，可能需要权限")
            except:
                print("   ? 无法检查用户组信息")
            return True
        except Exception as e:
            print(f"   ✗ 权限检查失败: {e}")
            return False
    
    def test_open_close(self):
        """测试串口打开和关闭"""
        print("3. 测试串口打开/关闭...")
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"   ✓ 串口打开成功 - 端口: {self.port}, 波特率: {self.baudrate}")
            
            # 检查串口状态
            print(f"   串口状态: 可读={self.connection.readable()}, 可写={self.connection.writable()}")
            print(f"   缓冲区: 输入={self.connection.in_waiting}, 输出={self.connection.out_waiting}")
            
            self.connection.close()
            print("   ✓ 串口关闭成功")
            return True
        except Exception as e:
            print(f"   ✗ 串口操作失败: {e}")
            return False
    
    def create_test_packet(self, cmd, data):
        """创建测试数据包"""
        header = bytes([0xAA, 0x55])  # 帧头
        cmd_byte = bytes([cmd])       # 指令
        data_len = len(data)
        len_byte = bytes([data_len])  # 数据长度
        
        # 校验和
        checksum = cmd + data_len
        for byte in data:
            checksum += byte
        checksum = checksum & 0xFF
        checksum_byte = bytes([checksum])
        
        return header + cmd_byte + len_byte + data + checksum_byte
    
    def test_data_sending(self):
        """测试数据发送"""
        print("4. 测试数据发送...")
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            
            # 测试1: 发送激光点坐标 (0x02命令)
            print("   测试激光点坐标发送...")
            x, y = 100, 200
            data = struct.pack('<HH', x, y)  # 小端字节序
            packet = self.create_test_packet(0x02, data)
            
            print(f"   发送数据包: {packet.hex().upper()}")
            print(f"   解析: 帧头={packet[0:2].hex()}, 命令={packet[2]:02X}, 长度={packet[3]}, 数据={packet[4:8].hex()}, 校验={packet[8]:02X}")
            
            self.connection.write(packet)
            print(f"   ✓ 激光点坐标发送成功: ({x}, {y})")
            
            time.sleep(0.1)
            
            # 测试2: 发送矩形顶点 (0x01命令)
            print("   测试矩形顶点发送...")
            points = [(50, 60), (150, 70), (140, 170), (60, 160)]
            data = bytes()
            for (px, py) in points:
                data += struct.pack('<HH', px, py)
            
            packet = self.create_test_packet(0x01, data)
            print(f"   发送数据包: {packet.hex().upper()}")
            
            self.connection.write(packet)
            print(f"   ✓ 矩形顶点发送成功: {points}")
            
            self.connection.close()
            return True
            
        except Exception as e:
            print(f"   ✗ 数据发送失败: {e}")
            if self.connection:
                self.connection.close()
            return False
    
    def test_continuous_sending(self):
        """测试连续发送"""
        print("5. 测试连续发送 (5秒)...")
        try:
            self.connection = serial.Serial(self.port, self.baudrate, timeout=1)
            
            start_time = time.time()
            count = 0
            
            while time.time() - start_time < 5:
                # 模拟移动的激光点
                x = int(100 + 50 * (time.time() - start_time))
                y = int(100 + 30 * (time.time() - start_time))
                
                data = struct.pack('<HH', x, y)
                packet = self.create_test_packet(0x02, data)
                
                self.connection.write(packet)
                count += 1
                
                print(f"   发送 #{count}: ({x}, {y})")
                time.sleep(0.1)  # 10Hz发送频率
            
            self.connection.close()
            print(f"   ✓ 连续发送完成，共发送 {count} 次")
            return True
            
        except Exception as e:
            print(f"   ✗ 连续发送失败: {e}")
            if self.connection:
                self.connection.close()
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=== 串口通信测试开始 ===\n")
        
        tests = [
            self.check_port_exists,
            self.check_permissions,
            self.test_open_close,
            self.test_data_sending,
            self.test_continuous_sending
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
                print()
            except Exception as e:
                print(f"   ✗ 测试异常: {e}\n")
                results.append(False)
        
        # 总结
        print("=== 测试结果总结 ===")
        test_names = [
            "串口设备检查",
            "权限检查", 
            "打开/关闭测试",
            "数据发送测试",
            "连续发送测试"
        ]
        
        for i, (name, result) in enumerate(zip(test_names, results)):
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{i+1}. {name}: {status}")
        
        success_count = sum(results)
        print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
        
        if success_count == len(results):
            print("🎉 所有测试通过！串口通信正常")
        else:
            print("⚠️  部分测试失败，请检查相关问题")

def main():
    """主函数"""
    print("串口测试工具")
    print("用途: 全面测试串口通信功能")
    print("默认配置: /dev/ttyTHS0, 115200波特率\n")
    
    # 可以通过命令行参数指定串口
    port = sys.argv[1] if len(sys.argv) > 1 else '/dev/ttyTHS0'
    baudrate = int(sys.argv[2]) if len(sys.argv) > 2 else 115200
    
    print(f"测试配置: 端口={port}, 波特率={baudrate}\n")
    
    tester = SerialTester(port, baudrate)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
